"use client"

import React, { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  Send,
  Trash2,
  Brain,
  User,
  Sparkles,
  Loader2,
  MessageSquare,
  Zap,
  ChevronDown,
  ChevronUp
} from 'lucide-react';
import { LangChainChatMessage } from '@/hooks/useLangChainRag';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { vscDarkPlus } from 'react-syntax-highlighter/dist/esm/styles/prism';
import { cn } from '@/lib/utils';
import AgenticThinkingIndicator from './AgenticThinkingIndicator';
import AgenticSourceReference from './AgenticSourceReference';

interface AgenticChatInterfaceProps {
  messages: LangChainChatMessage[];
  onSendMessage: (message: string) => Promise<void>;
  onClearChat: () => void;
  isLoading: boolean;
  disabled?: boolean;
  sourceDocuments?: any[];
  useDeepResearch?: boolean;
  deepResearchIterations?: number;
  selectedModel: string;
}

const AgenticChatInterface: React.FC<AgenticChatInterfaceProps> = ({
  messages,
  onSendMessage,
  onClearChat,
  isLoading,
  disabled = false,
  sourceDocuments = [],
  useDeepResearch = false,
  deepResearchIterations = 0,
  selectedModel
}) => {
  const [input, setInput] = useState('');
  const [showSources, setShowSources] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  // Scroll to bottom when messages change
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // Focus input on mount
  useEffect(() => {
    if (!disabled) {
      inputRef.current?.focus();
    }
  }, [disabled]);

  const handleSendMessage = async () => {
    if (input.trim() && !isLoading && !disabled) {
      const message = input;
      setInput('');
      await onSendMessage(message);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const getModelIcon = (model: string) => {
    if (model.startsWith('gemini')) return Sparkles;
    if (model.startsWith('command')) return Brain;
    return Zap;
  };

  const ModelIcon = getModelIcon(selectedModel);

  return (
    <Card className="h-full flex flex-col">
      {/* Header */}
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <MessageSquare className="h-5 w-5 text-primary" />
            <h3 className="font-semibold">AI Research Chat</h3>
            {useDeepResearch && (
              <Badge variant="outline" className="text-xs">
                <Brain className="h-3 w-3 mr-1" />
                Deep Research
              </Badge>
            )}
          </div>

          <div className="flex items-center gap-2">
            {sourceDocuments.length > 0 && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowSources(!showSources)}
                className="text-xs h-7"
              >
                Sources ({sourceDocuments.length})
                {showSources ? (
                  <ChevronUp className="h-3 w-3 ml-1" />
                ) : (
                  <ChevronDown className="h-3 w-3 ml-1" />
                )}
              </Button>
            )}

            <Button
              variant="ghost"
              size="sm"
              onClick={onClearChat}
              disabled={messages.length === 0 || isLoading || disabled}
              className="text-xs h-7"
            >
              <Trash2 className="h-3 w-3 mr-1" />
              Clear
            </Button>
          </div>
        </div>

        {/* Sources Panel */}
        {showSources && sourceDocuments.length > 0 && (
          <div className="mt-3 pt-3 border-t">
            <AgenticSourceReference sourceDocuments={sourceDocuments} />
          </div>
        )}
      </CardHeader>

      {/* Messages */}
      <CardContent className="flex-1 p-0 overflow-hidden">
        <ScrollArea className="h-full px-4">
          {messages.length === 0 ? (
            <div className="flex flex-col items-center justify-center h-full text-center p-8">
              <div className="bg-primary/10 rounded-full p-4 mb-4">
                <Brain className="h-8 w-8 text-primary" />
              </div>
              <h4 className="font-medium mb-2">Ready to Research</h4>
              {disabled ? (
                <p className="text-sm text-muted-foreground max-w-md">
                  Select and embed datasets or PDF documents from the sidebar to start your AI-powered research session.
                </p>
              ) : (
                <p className="text-sm text-muted-foreground max-w-md">
                  Ask questions about your data and get intelligent insights with source references.
                  The AI will analyze your documents and provide accurate, contextual answers.
                </p>
              )}
            </div>
          ) : (
            <div className="space-y-4 py-4">
              {/* Thinking Indicator */}
              {isLoading && useDeepResearch && (
                <AgenticThinkingIndicator
                  isVisible={true}
                  iterations={deepResearchIterations}
                  model={selectedModel}
                />
              )}

              {messages.map((message, index) => (
                <div
                  key={index}
                  className={cn(
                    "flex gap-3",
                    message.role === 'user' ? "justify-end" : "justify-start"
                  )}
                >
                  {message.role === 'assistant' && (
                    <div className="flex-shrink-0">
                      <div className="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center">
                        <ModelIcon className="h-4 w-4 text-primary" />
                      </div>
                    </div>
                  )}

                  <div
                    className={cn(
                      "max-w-[80%] rounded-lg px-4 py-3",
                      message.role === 'user'
                        ? "bg-primary text-primary-foreground ml-12"
                        : "bg-muted/50 mr-12"
                    )}
                  >
                    <div className="prose dark:prose-invert max-w-none prose-sm">
                      <ReactMarkdown
                        remarkPlugins={[remarkGfm]}
                        components={{
                          code({ node, className, children, ...props }: any) {
                            try {
                              const match = /language-(\w+)/.exec(className || '');
                              const isInline = !match;
                              const childrenStr = typeof children === 'string' ? children : String(children || '');

                              const sanitizedChildren = childrenStr.endsWith('\n') ?
                                childrenStr.substring(0, childrenStr.length - 1) :
                                childrenStr;

                              return !isInline && match ? (
                                <SyntaxHighlighter
                                  language={match[1]}
                                  // @ts-ignore
                                  style={vscDarkPlus}
                                  PreTag="div"
                                  {...props}
                                >
                                  {sanitizedChildren}
                                </SyntaxHighlighter>
                              ) : (
                                <code className={className} {...props}>
                                  {childrenStr}
                                </code>
                              );
                            } catch (error) {
                              console.error('Error in code renderer:', error);
                              return <code className={className || ''} {...props}>{String(children || '')}</code>;
                            }
                          }
                        }}
                      >
                        {typeof message.content === 'string'
                          ? message.content
                          : JSON.stringify(message.content)}
                      </ReactMarkdown>
                    </div>
                  </div>

                  {message.role === 'user' && (
                    <div className="flex-shrink-0">
                      <div className="w-8 h-8 rounded-full bg-primary flex items-center justify-center">
                        <User className="h-4 w-4 text-primary-foreground" />
                      </div>
                    </div>
                  )}
                </div>
              ))}

              {/* Loading indicator for non-deep research */}
              {isLoading && !useDeepResearch && (
                <div className="flex gap-3 justify-start">
                  <div className="flex-shrink-0">
                    <div className="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center">
                      <Loader2 className="h-4 w-4 text-primary animate-spin" />
                    </div>
                  </div>
                  <div className="bg-muted/50 rounded-lg px-4 py-3 mr-12">
                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                      <Loader2 className="h-3 w-3 animate-spin" />
                      Analyzing your data...
                    </div>
                  </div>
                </div>
              )}

              <div ref={messagesEndRef} />
            </div>
          )}
        </ScrollArea>
      </CardContent>

      {/* Input */}
      <div className="p-4 border-t">
        {disabled && (
          <div className="mb-3 p-2 text-xs bg-amber-50/50 dark:bg-amber-950/10 border border-amber-100 dark:border-amber-900/50 rounded-md">
            <p className="text-amber-700 dark:text-amber-300 flex items-center gap-1">
              <Brain className="h-3 w-3" />
              Select and embed data sources to start your research session
            </p>
          </div>
        )}

        <div className="flex items-center gap-2">
          <Input
            ref={inputRef}
            placeholder={disabled ? "Select data sources first..." : "Ask anything about your data..."}
            value={input}
            onChange={(e) => setInput(e.target.value)}
            onKeyDown={handleKeyDown}
            disabled={isLoading || disabled}
            className="flex-1"
          />
          <Button
            onClick={handleSendMessage}
            disabled={!input.trim() || isLoading || disabled}
            size="sm"
            className="px-3"
          >
            {isLoading ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <Send className="h-4 w-4" />
            )}
          </Button>
        </div>
      </div>
    </Card>
  );
};

export default AgenticChatInterface;
