"use client";

import React, { useState, useEffect, useRef } from 'react'
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import { createChannel, sendMessage, addReaction, getChannels, getMessages, generateInviteLink, getChannelMembers, deleteMessage } from '@/actions/chatActions'
import { Hash, Plus, Send, Paperclip, Smile, MoreVertical, Search, Info, Settings, Users, Bell, Phone, Video, Star, MessageSquare } from 'lucide-react';
import { toast } from 'sonner';
import { useUser } from "@clerk/nextjs";
import { cn } from "@/lib/utils";
import { ConnectionStatus } from './ConnectionStatus';
import { SlackMessage } from './SlackMessage';
import { pusherClient } from '@/lib/pusher';
import { TypingIndicator } from './TypingIndicator';
import { SlackFileUpload } from './SlackFileUpload';
import { ChatMessage } from '@/types/index';

// UI Components
import { But<PERSON> } from "@/components/ui/button"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Input } from "@/components/ui/input"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Separator } from "@/components/ui/separator"
import { Badge } from "@/components/ui/badge"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"

// Types
interface Channel {
  id: string;
  name: string;
  unreadCount: number;
}

interface ChannelMember {
  id: string;
  name: string;
  avatar?: string;
  isOnline?: boolean;
  lastSeen?: Date;
}

interface ChatEvent {
  type: 'message' | 'messageDeleted' | 'typing';
  channelId: string;
  data: any;
}

function SlackChatInterface() {
  const params = useParams();
  const router = useRouter();
  const { user } = useUser();
  const channelId = params?.id as string;
  
  // State
  const [channels, setChannels] = useState<Channel[]>([]);
  const [activeChannel, setActiveChannel] = useState<Channel | null>(null);
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [newMessage, setNewMessage] = useState('');
  const [channelMembers, setChannelMembers] = useState<ChannelMember[]>([]);
  const [isConnected, setIsConnected] = useState(false);
  const [typingUsers, setTypingUsers] = useState<Set<string>>(new Set());
  const [showFileUpload, setShowFileUpload] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [replyingTo, setReplyingTo] = useState<ChatMessage | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [showChannelInfo, setShowChannelInfo] = useState(false);
  const [isLoadingMessages, setIsLoadingMessages] = useState(false);
  const [isLoadingChannels, setIsLoadingChannels] = useState(true);
  
  // Refs
  const scrollRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Current user data
  const currentUser = user ? {
    id: user.id,
    fullName: user.fullName || user.username || 'Anonymous',
    username: user.username || 'anonymous',
    avatar: user.imageUrl || ''
  } : null;

  // User colors for avatars
  const userColors = {
    owner: "bg-blue-500 text-white",
    member: "bg-green-500 text-white",
    guest: "bg-gray-500 text-white"
  };

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // Focus input when component mounts
  useEffect(() => {
    inputRef.current?.focus();
  }, []);

  // Handle typing indicators
  useEffect(() => {
    let typingTimer: NodeJS.Timeout | undefined;

    const handleTyping = () => {
      if (channelId && currentUser && newMessage.trim()) {
        // Send typing indicator via API instead of direct pusher trigger
        fetch('/api/slackchat/typing', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            channelId,
            userId: currentUser.id,
            userName: currentUser.fullName
          })
        }).catch(console.error);
      }
    };

    if (newMessage.trim()) {
      handleTyping();
      if (typingTimer) {
        clearTimeout(typingTimer);
      }
      typingTimer = setTimeout(() => {
        // Stop typing indicator after 3 seconds
      }, 3000);
    }

    return () => {
      if (typingTimer) {
        clearTimeout(typingTimer);
      }
    };
  }, [newMessage, channelId, currentUser]);

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Escape to cancel reply
      if (e.key === 'Escape' && replyingTo) {
        setReplyingTo(null);
      }

      // Ctrl/Cmd + K to focus search
      if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
        e.preventDefault();
        // Focus search input if it exists
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [replyingTo]);

  // Fetch channels on mount
  useEffect(() => {
    fetchChannels();
  }, []);

  // Set active channel when channelId changes
  useEffect(() => {
    if (channelId && channels.length > 0) {
      const channel = channels.find(c => c.id === channelId);
      if (channel) {
        setActiveChannel(channel);
        fetchMessages(channelId);
        fetchChannelMembers(channelId);
      }
    }
  }, [channelId, channels]);

  // Pusher connection for real-time updates
  useEffect(() => {
    if (!channelId) return;

    const channel = pusherClient.subscribe(`chat-${channelId}`);
    setIsConnected(true);

    // Handle new messages
    channel.bind('message', (data: ChatEvent) => {
      setMessages(prev => {
        if (prev.some(m => m.id === data.data.id)) return prev;
        return [...prev, data.data];
      });
    });

    // Handle deleted messages
    channel.bind('messageDeleted', (data: ChatEvent) => {
      setMessages(prev => prev.filter(m => m.id !== data.data.messageId));
    });

    // Handle typing indicators
    channel.bind('typing', (data: any) => {
      if (data.userId !== currentUser?.id) {
        setTypingUsers(prev => new Set([...prev, data.userName]));
        setTimeout(() => {
          setTypingUsers(prev => {
            const newSet = new Set(prev);
            newSet.delete(data.userName);
            return newSet;
          });
        }, 3000);
      }
    });

    return () => {
      pusherClient.unsubscribe(`chat-${channelId}`);
      setIsConnected(false);
    };
  }, [channelId, currentUser?.id]);

  const fetchChannels = async () => {
    try {
      setIsLoadingChannels(true);
      const result = await getChannels();
      if (result.success) {
        setChannels(result.channels.map(channel => ({
          id: channel.id,
          name: channel.name,
          unreadCount: 0
        })));

        if (result.channels.length > 0 && !channelId) {
          router.push(`/hr/workspace/chats/${result.channels[0].id}`);
        }
      } else {
        toast.error("Failed to fetch channels");
      }
    } catch (error) {
      console.error('Error fetching channels:', error);
      toast.error("An error occurred while fetching channels");
    } finally {
      setIsLoadingChannels(false);
    }
  };

  const fetchMessages = async (id: string) => {
    try {
      setIsLoadingMessages(true);
      const result = await getMessages(id);
      if (result.success && result.messages) {
        setMessages(result.messages as ChatMessage[]);
      } else {
        console.error(result.error);
        toast.error("Failed to fetch messages");
      }
    } catch (error) {
      console.error('Error fetching messages:', error);
      toast.error("An error occurred while fetching messages");
    } finally {
      setIsLoadingMessages(false);
    }
  };

  const fetchChannelMembers = async (id: string) => {
    try {
      const result = await getChannelMembers(id);
      if (result.success) {
        setChannelMembers(result.members);
      } else {
        toast.error("Failed to fetch channel members");
      }
    } catch (error) {
      console.error('Error fetching channel members:', error);
      toast.error("An error occurred while fetching channel members");
    }
  };

  const handleChannelClick = (channel: Channel) => {
    router.push(`/hr/workspace/chats/${channel.id}`);
  };

  const handleSendMessage = async (e: React.FormEvent, fileUrl?: string) => {
    e.preventDefault();
    if (!activeChannel || !currentUser || (!newMessage.trim() && !fileUrl)) return;

    const messageData = {
      channelId: activeChannel.id,
      content: newMessage.trim(),
      userId: currentUser.id,
      fileKey: fileUrl ? fileUrl.split('/').pop() : undefined,
      fileType: fileUrl ? getFileType(fileUrl) : undefined,
      parentId: replyingTo?.id
    };

    try {
      const result = await sendMessage(messageData);
      if (result.success) {
        setNewMessage('');
        setShowFileUpload(false);
        setReplyingTo(null);
        inputRef.current?.focus();
        toast.success("Message sent!");
      } else {
        toast.error(result.error || "Failed to send message");
      }
    } catch (error) {
      toast.error("An error occurred while sending the message");
    }
  };

  const handleFileSend = async (fileUrl: string, fileType?: string) => {
    if (!activeChannel || !currentUser) return;

    const messageData = {
      channelId: activeChannel.id,
      content: newMessage.trim(),
      userId: currentUser.id,
      fileKey: fileUrl.split('/').pop(),
      fileType: fileType || getFileType(fileUrl),
      parentId: replyingTo?.id
    };

    try {
      const result = await sendMessage(messageData);
      if (result.success) {
        setNewMessage('');
        setShowFileUpload(false);
        setReplyingTo(null);
        inputRef.current?.focus();
        toast.success("File sent successfully!");
      } else {
        toast.error(result.error || "Failed to send file");
      }
    } catch (error) {
      toast.error("An error occurred while sending the file");
    }
  };

  const handleDeleteMessage = async (messageId: string) => {
    if (!activeChannel) return;

    try {
      const result = await deleteMessage(messageId, activeChannel.id);
      if (result.success) {
        setMessages(prev => prev.filter(m => m.id !== messageId));
        toast.success("Message deleted");
      } else {
        toast.error("Failed to delete message");
      }
    } catch (error) {
      toast.error("An error occurred while deleting the message");
    }
  };

  const handleReply = (messageId: string) => {
    const message = messages.find(m => m.id === messageId);
    if (message) {
      setReplyingTo(message);
      inputRef.current?.focus();
    }
  };

  const handleReaction = async (messageId: string, emoji: string) => {
    try {
      const result = await addReaction(messageId, emoji);
      if (result.success) {
        // Fetch updated messages to get the latest reactions
        if (activeChannel) {
          fetchMessages(activeChannel.id);
        }
        toast.success("Reaction added");
      }
    } catch (error) {
      toast.error("Failed to add reaction");
    }
  };

  const getFileType = (url: string) => {
    const extension = url.split('.').pop()?.toLowerCase();
    if (['jpg', 'jpeg', 'png', 'gif', 'webp'].includes(extension || '')) return 'image';
    if (extension === 'pdf') return 'pdf';
    if (['csv', 'xlsx', 'xls'].includes(extension || '')) return 'spreadsheet';
    return 'other';
  };

  const formatTime = (timestamp: string) => {
    const date = new Date(timestamp);
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  const getRelativeTime = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);
    
    if (diffInSeconds < 60) return 'now';
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m`;
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h`;
    return `${Math.floor(diffInSeconds / 86400)}d`;
  };

  return (
    <TooltipProvider>
      <div className="h-screen flex bg-background">
        {/* Sidebar */}
        <div className="w-64 bg-slate-800 text-white flex flex-col">
          {/* Workspace Header */}
          <div className="p-4 border-b border-slate-700">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="font-bold text-lg">HR Atlas</h1>
                <div className="flex items-center gap-2 text-sm text-slate-300">
                  <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                  <span>{currentUser?.fullName}</span>
                </div>
              </div>
              <Button variant="ghost" size="icon" className="text-slate-300 hover:text-white hover:bg-slate-700">
                <MoreVertical className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {/* Channels Section */}
          <div className="flex-1 overflow-hidden">
            <div className="p-3">
              <div className="flex items-center justify-between mb-3">
                <h2 className="text-sm font-semibold text-slate-300 uppercase tracking-wide">Channels</h2>
                <Button variant="ghost" size="icon" className="h-6 w-6 text-slate-400 hover:text-white hover:bg-slate-700">
                  <Plus className="h-4 w-4" />
                </Button>
              </div>
              
              <ScrollArea className="h-[200px]">
                <div className="space-y-1">
                  {isLoadingChannels ? (
                    <div className="space-y-2">
                      {[1, 2, 3].map(i => (
                        <div key={i} className="flex items-center gap-2 px-2 py-1.5">
                          <div className="w-4 h-4 bg-slate-600 rounded animate-pulse" />
                          <div className="h-4 bg-slate-600 rounded animate-pulse flex-1" />
                        </div>
                      ))}
                    </div>
                  ) : channels.length > 0 ? (
                    channels.map(channel => (
                      <button
                        key={channel.id}
                        onClick={() => handleChannelClick(channel)}
                        className={cn(
                          "w-full flex items-center gap-2 px-2 py-1.5 rounded text-sm transition-colors",
                          activeChannel?.id === channel.id
                            ? "bg-blue-600 text-white font-medium"
                            : "text-slate-300 hover:bg-slate-700 hover:text-white"
                        )}
                      >
                        <Hash className="h-4 w-4" />
                        <span className="truncate">{channel.name}</span>
                        {channel.unreadCount > 0 && (
                          <Badge variant="destructive" className="ml-auto h-5 text-xs">
                            {channel.unreadCount}
                          </Badge>
                        )}
                      </button>
                    ))
                  ) : (
                    <div className="text-center py-4 text-slate-400 text-sm">
                      No channels found
                    </div>
                  )}
                </div>
              </ScrollArea>
            </div>

            <Separator className="bg-slate-700" />

            {/* Members Section */}
            <div className="p-3">
              <h2 className="text-sm font-semibold text-slate-300 uppercase tracking-wide mb-3">
                Members ({channelMembers.length})
              </h2>
              
              <ScrollArea className="h-[calc(100vh-400px)]">
                <div className="space-y-1">
                  {channelMembers.map(member => (
                    <div 
                      key={member.id}
                      className="flex items-center gap-2 px-2 py-1.5 hover:bg-slate-700 rounded transition-colors"
                    >
                      <div className="relative">
                        <Avatar className="h-6 w-6">
                          <AvatarImage src={member.avatar} />
                          <AvatarFallback className="text-xs bg-slate-600">
                            {member.name.substring(0, 2).toUpperCase()}
                          </AvatarFallback>
                        </Avatar>
                        <div className={cn(
                          "absolute -bottom-0.5 -right-0.5 w-3 h-3 rounded-full border-2 border-slate-800",
                          member.isOnline ? "bg-green-400" : "bg-slate-500"
                        )} />
                      </div>
                      <div className="flex-1 min-w-0">
                        <span className="text-sm text-slate-300 truncate block">
                          {member.name}
                        </span>
                        {!member.isOnline && member.lastSeen && (
                          <span className="text-xs text-slate-500 truncate block">
                            {getRelativeTime(member.lastSeen.toISOString())}
                          </span>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </ScrollArea>
            </div>
          </div>
        </div>

        {/* Main Chat Area */}
        <div className="flex-1 flex flex-col">
          {/* Channel Header */}
          <div className="h-14 border-b bg-white flex items-center justify-between px-4">
            <div className="flex items-center gap-3">
              <div className="flex items-center gap-2">
                <Hash className="h-5 w-5 text-slate-500" />
                <h2 className="font-semibold text-lg">{activeChannel?.name}</h2>
              </div>
              <Separator orientation="vertical" className="h-6" />
              <div className="flex items-center gap-1 text-sm text-slate-600">
                <Users className="h-4 w-4" />
                <span>{channelMembers.length}</span>
              </div>
            </div>
            
            <div className="flex items-center gap-2">
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button variant="ghost" size="icon" className="h-8 w-8">
                    <Phone className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>Start a call</TooltipContent>
              </Tooltip>
              
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button variant="ghost" size="icon" className="h-8 w-8">
                    <Video className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>Start a video call</TooltipContent>
              </Tooltip>
              
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button variant="ghost" size="icon" className="h-8 w-8">
                    <Star className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>Star channel</TooltipContent>
              </Tooltip>
              
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button 
                    variant="ghost" 
                    size="icon" 
                    className="h-8 w-8"
                    onClick={() => setShowChannelInfo(!showChannelInfo)}
                  >
                    <Info className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>Channel details</TooltipContent>
              </Tooltip>
              
              <ConnectionStatus isConnected={isConnected} />
            </div>
          </div>

          {/* Messages Area */}
          <div className="flex-1 flex">
            <div className="flex-1 flex flex-col">
              <ScrollArea className="flex-1 px-4">
                <div className="py-4 space-y-1">
                  {isLoadingMessages ? (
                    <div className="space-y-4">
                      {[1, 2, 3, 4, 5].map(i => (
                        <div key={i} className="flex items-start gap-3">
                          <div className="w-9 h-9 bg-slate-200 rounded-full animate-pulse" />
                          <div className="flex-1 space-y-2">
                            <div className="flex items-center gap-2">
                              <div className="h-4 bg-slate-200 rounded w-20 animate-pulse" />
                              <div className="h-3 bg-slate-200 rounded w-16 animate-pulse" />
                            </div>
                            <div className="h-4 bg-slate-200 rounded w-3/4 animate-pulse" />
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : messages.length > 0 ? (
                    messages.map((message: ChatMessage, index: number) => (
                      <SlackMessage
                        key={message.id}
                        message={message}
                        channelId={channelId}
                        isCurrentUser={message.userId === currentUser?.id}
                        userColors={userColors}
                        onDelete={handleDeleteMessage}
                        onReply={handleReply}
                        onReaction={handleReaction}
                        replyTo={message.replyTo}
                        readBy={message.readBy}
                        previousMessage={index > 0 ? messages[index - 1] : null}
                      />
                    ))
                  ) : (
                    <div className="flex-1 flex items-center justify-center">
                      <div className="text-center text-slate-500">
                        <MessageSquare className="h-12 w-12 mx-auto mb-4 text-slate-300" />
                        <h3 className="text-lg font-medium mb-2">No messages yet</h3>
                        <p className="text-sm">Be the first to send a message in #{activeChannel?.name}</p>
                      </div>
                    </div>
                  )}

                  {/* Typing Indicator */}
                  {typingUsers.size > 0 && (
                    <TypingIndicator
                      userName={Array.from(typingUsers).join(', ')}
                    />
                  )}

                  <div ref={messagesEndRef} />
                </div>
              </ScrollArea>

              {/* Reply Banner */}
              {replyingTo && (
                <div className="px-4 py-2 bg-slate-50 border-t border-l-4 border-l-blue-500">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2 text-sm">
                      <span className="text-slate-600">Replying to</span>
                      <span className="font-medium text-blue-600">{replyingTo.user}</span>
                      <span className="text-slate-500 truncate max-w-xs">
                        {replyingTo.content}
                      </span>
                    </div>
                    <Button 
                      variant="ghost" 
                      size="sm" 
                      onClick={() => setReplyingTo(null)}
                      className="h-6 w-6 p-0"
                    >
                      ×
                    </Button>
                  </div>
                </div>
              )}

              {/* Message Input */}
              <div className="p-4 border-t bg-white">
                {showFileUpload ? (
                  <SlackFileUpload
                    onFileSelect={handleFileSend}
                    onCancel={() => setShowFileUpload(false)}
                    isUploading={isUploading}
                    onMessageSubmit={handleSendMessage}
                    message={newMessage}
                    onMessageChange={(e) => setNewMessage(e.target.value)}
                    placeholder={`Message #${activeChannel?.name || 'channel'}`}
                  />
                ) : (
                  <form onSubmit={handleSendMessage} className="flex items-end gap-3">
                    <div className="flex-1 relative">
                      <div className="flex items-center gap-2 p-3 border rounded-lg bg-white focus-within:ring-2 focus-within:ring-blue-500 focus-within:border-blue-500">
                        <Button
                          type="button"
                          variant="ghost"
                          size="icon"
                          className="h-8 w-8 text-slate-500 hover:text-slate-700"
                          onClick={() => setShowFileUpload(true)}
                        >
                          <Paperclip className="h-4 w-4" />
                        </Button>
                        
                        <Input
                          ref={inputRef}
                          type="text"
                          placeholder={`Message #${activeChannel?.name || 'channel'}`}
                          value={newMessage}
                          onChange={(e) => setNewMessage(e.target.value)}
                          className="flex-1 border-0 focus-visible:ring-0 focus-visible:ring-offset-0 p-0"
                        />
                        
                        <Button
                          type="button"
                          variant="ghost"
                          size="icon"
                          className="h-8 w-8 text-slate-500 hover:text-slate-700"
                        >
                          <Smile className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                    
                    <Button 
                      type="submit" 
                      disabled={!newMessage.trim() && !showFileUpload}
                      className="h-10 px-4"
                    >
                      <Send className="h-4 w-4" />
                    </Button>
                  </form>
                )}
              </div>
            </div>

            {/* Channel Info Sidebar */}
            {showChannelInfo && (
              <div className="w-80 border-l bg-slate-50 p-4">
                <div className="space-y-4">
                  <div>
                    <h3 className="font-semibold text-lg mb-2">About this channel</h3>
                    <div className="flex items-center gap-2 text-sm text-slate-600">
                      <Hash className="h-4 w-4" />
                      <span>{activeChannel?.name}</span>
                    </div>
                  </div>
                  
                  <Separator />
                  
                  <div>
                    <h4 className="font-medium mb-2">Members ({channelMembers.length})</h4>
                    <div className="space-y-2">
                      {channelMembers.slice(0, 5).map(member => (
                        <div key={member.id} className="flex items-center gap-2">
                          <Avatar className="h-6 w-6">
                            <AvatarImage src={member.avatar} />
                            <AvatarFallback className="text-xs">
                              {member.name.substring(0, 2).toUpperCase()}
                            </AvatarFallback>
                          </Avatar>
                          <span className="text-sm">{member.name}</span>
                          {member.isOnline && (
                            <div className="w-2 h-2 bg-green-400 rounded-full" />
                          )}
                        </div>
                      ))}
                      {channelMembers.length > 5 && (
                        <Button variant="ghost" size="sm" className="text-blue-600">
                          View all {channelMembers.length} members
                        </Button>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </TooltipProvider>
  );
}

export default SlackChatInterface;
