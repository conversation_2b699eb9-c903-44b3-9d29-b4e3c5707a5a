"use client";

import React, { useState, useEffect, useRef } from 'react'
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import { createChannel, sendMessage, addReaction, getChannels, getMessages, generateInviteLink, getChannelMembers, deleteMessage } from '@/actions/chatActions'
import { Hash, Plus, Send, Paperclip, Smile, MoreVertical, Search, Info, Settings, Users, Bell, Phone, Video, Star, MessageSquare, Kanban, Layout, X } from 'lucide-react';
import { toast } from 'sonner';
import { useUser } from "@clerk/nextjs";
import { cn } from "@/lib/utils";
import { ConnectionStatus } from './ConnectionStatus';
import { SlackMessage } from './SlackMessage';
import { ChatSidebar } from './ChatSidebar';
import { pusherClient } from '@/lib/pusher';
import { TypingIndicator } from './TypingIndicator';
import { SlackFileUpload } from './SlackFileUpload';
import { ChatMessage } from '@/types/index';

// UI Components
import { Button } from "@/components/ui/button"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Input } from "@/components/ui/input"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Separator } from "@/components/ui/separator"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"

// Kanban Components
import KanbanBoard from '@/components/Managements/KanbanBoard'

// Types
interface Channel {
  id: string;
  name: string;
  unreadCount: number;
}

interface ChannelMember {
  id: string;
  name: string;
  avatar?: string;
  isOnline?: boolean;
  lastSeen?: Date;
}

interface ChatEvent {
  type: 'message' | 'messageDeleted' | 'typing';
  channelId: string;
  data: any;
}

function SlackChatInterface() {
  const params = useParams();
  const router = useRouter();
  const { user } = useUser();
  const channelId = params?.id as string;
  
  // State
  const [channels, setChannels] = useState<Channel[]>([]);
  const [activeChannel, setActiveChannel] = useState<Channel | null>(null);
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [newMessage, setNewMessage] = useState('');
  const [channelMembers, setChannelMembers] = useState<ChannelMember[]>([]);
  const [isConnected, setIsConnected] = useState(false);
  const [typingUsers, setTypingUsers] = useState<Set<string>>(new Set());
  const [showFileUpload, setShowFileUpload] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [replyingTo, setReplyingTo] = useState<ChatMessage | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [showChannelInfo, setShowChannelInfo] = useState(false);
  const [isLoadingMessages, setIsLoadingMessages] = useState(false);
  const [isLoadingChannels, setIsLoadingChannels] = useState(true);
  const [showKanban, setShowKanban] = useState(false);
  const [activeView, setActiveView] = useState<'chat' | 'kanban' | 'split'>('chat');
  const [notificationsEnabled, setNotificationsEnabled] = useState(true);
  const [soundEnabled, setSoundEnabled] = useState(true);
  
  // Refs
  const inputRef = useRef<HTMLInputElement>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Current user data
  const currentUser = user ? {
    id: user.id,
    fullName: user.fullName || user.username || 'Anonymous',
    username: user.username || 'anonymous',
    avatar: user.imageUrl || ''
  } : null;

  // User colors for avatars
  const userColors = {
    owner: "bg-blue-500 text-white",
    member: "bg-green-500 text-white",
    guest: "bg-gray-500 text-white"
  };

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // Focus input when component mounts
  useEffect(() => {
    inputRef.current?.focus();
  }, []);

  // Handle typing indicators
  useEffect(() => {
    let typingTimer: NodeJS.Timeout | undefined;

    const handleTyping = () => {
      if (channelId && currentUser && newMessage.trim()) {
        // Send typing indicator via API instead of direct pusher trigger
        fetch('/api/slackchat/typing', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            channelId,
            userId: currentUser.id,
            userName: currentUser.fullName
          })
        }).catch(console.error);
      }
    };

    if (newMessage.trim()) {
      handleTyping();
      if (typingTimer) {
        clearTimeout(typingTimer);
      }
      typingTimer = setTimeout(() => {
        // Stop typing indicator after 3 seconds
      }, 3000);
    }

    return () => {
      if (typingTimer) {
        clearTimeout(typingTimer);
      }
    };
  }, [newMessage, channelId, currentUser]);

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Escape to cancel reply
      if (e.key === 'Escape' && replyingTo) {
        setReplyingTo(null);
      }

      // Ctrl/Cmd + K to focus search
      if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
        e.preventDefault();
        // Focus search input if it exists
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [replyingTo]);

  // Fetch channels on mount
  useEffect(() => {
    fetchChannels();
  }, []);

  // Set active channel when channelId changes
  useEffect(() => {
    if (channelId && channels.length > 0) {
      const channel = channels.find(c => c.id === channelId);
      if (channel) {
        setActiveChannel(channel);
        fetchMessages(channelId);
        fetchChannelMembers(channelId);
      }
    }
  }, [channelId, channels]);

  // Enhanced notification functions
  const playNotificationSound = () => {
    if (!soundEnabled) return;

    try {
      const audio = new Audio('/notification.mp3');
      audio.volume = 0.3; // Reduced volume for better UX
      audio.preload = 'auto';

      // Play with user gesture fallback
      const playPromise = audio.play();
      if (playPromise !== undefined) {
        playPromise.catch(error => {
          console.log('Could not play notification sound:', error);
          // Fallback: try to play on next user interaction
          document.addEventListener('click', () => {
            audio.play().catch(() => {});
          }, { once: true });
        });
      }
    } catch (error) {
      console.log('Audio not supported:', error);
    }
  };

  const showDesktopNotification = (message: any) => {
    if (!notificationsEnabled || Notification.permission !== 'granted') return;

    try {
      const notification = new Notification(`New message from ${message.user}`, {
        body: message.content || 'Sent a file',
        icon: message.avatar || '/default-avatar.png',
        tag: `message-${message.id}`,
        badge: '/favicon.ico',
        silent: !soundEnabled, // Don't play system sound if our sound is enabled
        requireInteraction: false
      });

      // Auto-close notification after 5 seconds
      setTimeout(() => {
        notification.close();
      }, 5000);

      // Handle notification click
      notification.onclick = () => {
        window.focus();
        notification.close();
      };
    } catch (error) {
      console.log('Desktop notification error:', error);
    }
  };

  // Pusher connection for real-time updates
  useEffect(() => {
    if (!channelId) return;

    const channel = pusherClient.subscribe(`chat-${channelId}`);
    setIsConnected(true);

    // Handle new messages
    channel.bind('message', (data: ChatEvent) => {
      setMessages(prev => {
        if (prev.some(m => m.id === data.data.id)) return prev;

        // Handle notifications for new messages from other users
        if (data.data.userId !== currentUser?.id) {
          // Play notification sound
          playNotificationSound();

          // Show desktop notification
          showDesktopNotification(data.data);

          // Update document title with unread indicator
          if (document.hidden) {
            document.title = `(1) New message - HR Atlas`;

            // Reset title when user returns
            const handleVisibilityChange = () => {
              if (!document.hidden) {
                document.title = 'HR Atlas';
                document.removeEventListener('visibilitychange', handleVisibilityChange);
              }
            };
            document.addEventListener('visibilitychange', handleVisibilityChange);
          }
        }

        return [...prev, data.data];
      });
    });

    // Handle deleted messages
    channel.bind('messageDeleted', (data: ChatEvent) => {
      setMessages(prev => prev.filter(m => m.id !== data.data.messageId));
    });

    // Handle typing indicators
    channel.bind('typing', (data: any) => {
      if (data.userId !== currentUser?.id) {
        setTypingUsers(prev => new Set([...prev, data.userName]));
        setTimeout(() => {
          setTypingUsers(prev => {
            const newSet = new Set(prev);
            newSet.delete(data.userName);
            return newSet;
          });
        }, 3000);
      }
    });

    return () => {
      pusherClient.unsubscribe(`chat-${channelId}`);
      setIsConnected(false);
    };
  }, [channelId, currentUser?.id]);

  // Request notification permission on mount
  useEffect(() => {
    if ('Notification' in window && Notification.permission === 'default') {
      Notification.requestPermission();
    }
  }, []);

  const fetchChannels = async () => {
    try {
      setIsLoadingChannels(true);
      const result = await getChannels();
      if (result.success) {
        setChannels(result.channels.map(channel => ({
          id: channel.id,
          name: channel.name,
          unreadCount: 0
        })));

        if (result.channels.length > 0 && !channelId) {
          router.push(`/hr/workspace/chats/${result.channels[0].id}`);
        }
      } else {
        toast.error("Failed to fetch channels");
      }
    } catch (error) {
      console.error('Error fetching channels:', error);
      toast.error("An error occurred while fetching channels");
    } finally {
      setIsLoadingChannels(false);
    }
  };

  const fetchMessages = async (id: string) => {
    try {
      setIsLoadingMessages(true);
      const result = await getMessages(id);
      if (result.success && result.messages) {
        setMessages(result.messages as ChatMessage[]);
      } else {
        console.error(result.error);
        toast.error("Failed to fetch messages");
      }
    } catch (error) {
      console.error('Error fetching messages:', error);
      toast.error("An error occurred while fetching messages");
    } finally {
      setIsLoadingMessages(false);
    }
  };

  const fetchChannelMembers = async (id: string) => {
    try {
      const result = await getChannelMembers(id);
      if (result.success && result.members) {
        // @ts-ignore
        setChannelMembers(result.members as ChannelMember[]);
      } else {
        toast.error("Failed to fetch channel members");
      }
    } catch (error) {
      console.error('Error fetching channel members:', error);
      toast.error("An error occurred while fetching channel members");
    }
  };

  const handleChannelClick = (channel: Channel) => {
    router.push(`/hr/workspace/chats/${channel.id}`);
  };

  const handleSendMessage = async (e: React.FormEvent, fileUrl?: string) => {
    e.preventDefault();
    if (!activeChannel || !currentUser || (!newMessage.trim() && !fileUrl)) return;

    // Check for special commands
    const message = newMessage.trim();
    if (message.startsWith('/')) {
      handleSlashCommand(message);
      return;
    }

    const messageData = {
      channelId: activeChannel.id,
      content: message,
      userId: currentUser.id,
      fileKey: fileUrl ? fileUrl.split('/').pop() : undefined,
      fileType: fileUrl ? getFileType(fileUrl) : undefined,
      parentId: replyingTo?.id
    };

    try {
      const result = await sendMessage(messageData);
      if (result.success) {
        setNewMessage('');
        setShowFileUpload(false);
        setReplyingTo(null);
        inputRef.current?.focus();
        toast.success("Message sent!");
      } else {
        toast.error(result.error || "Failed to send message");
      }
    } catch (error) {
      toast.error("An error occurred while sending the message");
    }
  };

  const handleSlashCommand = async (command: string) => {
    const cmd = command.toLowerCase();

    if (cmd.startsWith('/kanban') || cmd.startsWith('/board') || cmd.startsWith('/project')) {
      // Send the kanban command as a message
      const messageData = {
        channelId: activeChannel!.id,
        content: command,
        userId: currentUser!.id,
        parentId: replyingTo?.id
      };

      try {
        const result = await sendMessage(messageData);
        if (result.success) {
          setNewMessage('');
          setReplyingTo(null);
          inputRef.current?.focus();
          toast.success("Kanban board shared!");
        }
      } catch (error) {
        toast.error("Failed to share kanban board");
      }
    } else if (cmd === '/help') {
      toast.info("Available commands: /kanban, /board, /project, /kanban-compact");
    } else {
      toast.error("Unknown command. Type /help for available commands.");
    }
  };

  const handleFileSend = async (fileUrl: string, fileType?: string) => {
    if (!activeChannel || !currentUser) return;

    const messageData = {
      channelId: activeChannel.id,
      content: newMessage.trim(),
      userId: currentUser.id,
      fileKey: fileUrl.split('/').pop(),
      fileType: fileType || getFileType(fileUrl),
      parentId: replyingTo?.id
    };

    try {
      const result = await sendMessage(messageData);
      if (result.success) {
        setNewMessage('');
        setShowFileUpload(false);
        setReplyingTo(null);
        inputRef.current?.focus();
        toast.success("File sent successfully!");
      } else {
        toast.error(result.error || "Failed to send file");
      }
    } catch (error) {
      toast.error("An error occurred while sending the file");
    }
  };

  const handleDeleteMessage = async (messageId: string) => {
    if (!activeChannel) return;

    try {
      const result = await deleteMessage(messageId, activeChannel.id);
      if (result.success) {
        setMessages(prev => prev.filter(m => m.id !== messageId));
        toast.success("Message deleted");
      } else {
        toast.error("Failed to delete message");
      }
    } catch (error) {
      toast.error("An error occurred while deleting the message");
    }
  };

  const handleReply = (messageId: string) => {
    const message = messages.find(m => m.id === messageId);
    if (message) {
      setReplyingTo(message);
      inputRef.current?.focus();
    }
  };

  const handleReaction = async (messageId: string, emoji: string) => {
    try {
      const result = await addReaction(messageId, emoji);
      if (result.success) {
        // Fetch updated messages to get the latest reactions
        if (activeChannel) {
          fetchMessages(activeChannel.id);
        }
        toast.success("Reaction added");
      }
    } catch (error) {
      toast.error("Failed to add reaction");
    }
  };

  const getFileType = (url: string) => {
    const extension = url.split('.').pop()?.toLowerCase();
    if (['jpg', 'jpeg', 'png', 'gif', 'webp'].includes(extension || '')) return 'image';
    if (extension === 'pdf') return 'pdf';
    if (['csv', 'xlsx', 'xls'].includes(extension || '')) return 'spreadsheet';
    return 'other';
  };

  const formatTime = (timestamp: string) => {
    const date = new Date(timestamp);
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  const getRelativeTime = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);
    
    if (diffInSeconds < 60) return 'now';
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m`;
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h`;
    return `${Math.floor(diffInSeconds / 86400)}d`;
  };

  return (
    <div className="h-screen flex bg-background">
      {/* Compact Sidebar */}
      <div className="w-60 bg-card border-r flex flex-col">
        {/* Workspace Header */}
        <div className="p-3 border-b">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="font-semibold text-sm">HR Atlas</h1>
              <div className="flex items-center gap-1 text-xs text-muted-foreground">
                <div className="w-1.5 h-1.5 bg-green-500 rounded-full"></div>
                <span>{currentUser?.fullName}</span>
              </div>
            </div>
            <div className="flex items-center gap-1">
              <Button
                variant="ghost"
                size="icon"
                className={cn("h-6 w-6", notificationsEnabled ? "text-foreground" : "text-muted-foreground")}
                onClick={() => setNotificationsEnabled(!notificationsEnabled)}
              >
                <Bell className="h-3 w-3" />
              </Button>

              <Button
                variant="ghost"
                size="icon"
                className={cn("h-6 w-6", soundEnabled ? "text-foreground" : "text-muted-foreground")}
                onClick={() => setSoundEnabled(!soundEnabled)}
              >
                <span className="text-xs">{soundEnabled ? "🔊" : "🔇"}</span>
              </Button>

              <Button variant="ghost" size="icon" className="h-6 w-6">
                <MoreVertical className="h-3 w-3" />
              </Button>
            </div>
          </div>
        </div>

        {/* Navigation Items */}
        <div className="p-2 border-b">
          <div className="space-y-1">
            <Button
              variant="ghost"
              size="sm"
              className="w-full justify-start h-7"
              onClick={() => router.push(`/hr/workspace/chats/${channelId}`)}
            >
              <MessageSquare className="h-3 w-3 mr-2" />
              <span className="text-xs">Messages</span>
            </Button>
            <Button
              variant="ghost"
              size="sm"
              className="w-full justify-start h-7"
              onClick={() => router.push(`/hr/workspace/kanban`)}
            >
              <Kanban className="h-3 w-3 mr-2" />
              <span className="text-xs">Project Board</span>
            </Button>
          </div>
        </div>

        {/* Channels */}
        <div className="flex-1 overflow-hidden">
          <div className="p-2">
            <div className="flex items-center justify-between mb-2">
              <h2 className="text-xs font-medium text-muted-foreground uppercase tracking-wide">Channels</h2>
              <Button variant="ghost" size="icon" className="h-5 w-5">
                <Plus className="h-3 w-3" />
              </Button>
            </div>

            <ScrollArea className="h-32">
              <div className="space-y-0.5">
                {isLoadingChannels ? (
                  <div className="space-y-1">
                    {[1, 2, 3].map(i => (
                      <div key={i} className="flex items-center gap-2 px-2 py-1">
                        <div className="w-3 h-3 bg-muted rounded animate-pulse" />
                        <div className="h-3 bg-muted rounded animate-pulse flex-1" />
                      </div>
                    ))}
                  </div>
                ) : channels.length > 0 ? (
                  channels.map(channel => (
                    <button
                      key={channel.id}
                      onClick={() => handleChannelClick(channel)}
                      className={cn(
                        "w-full flex items-center gap-2 px-2 py-1 rounded text-xs transition-colors",
                        activeChannel?.id === channel.id
                          ? "bg-accent text-accent-foreground font-medium"
                          : "text-muted-foreground hover:bg-accent/50 hover:text-foreground"
                      )}
                    >
                      <Hash className="h-3 w-3" />
                      <span className="truncate">{channel.name}</span>
                      {channel.unreadCount > 0 && (
                        <Badge variant="secondary" className="ml-auto h-4 text-xs px-1">
                          {channel.unreadCount}
                        </Badge>
                      )}
                    </button>
                  ))
                ) : (
                  <div className="text-center py-2 text-muted-foreground text-xs">
                    No channels found
                  </div>
                )}
              </div>
            </ScrollArea>
          </div>

          <Separator />

          {/* Members */}
          <div className="p-2">
            <h2 className="text-xs font-medium text-muted-foreground uppercase tracking-wide mb-2">
              Members ({channelMembers.length})
            </h2>

            <ScrollArea className="h-40">
              <div className="space-y-0.5">
                {channelMembers.map(member => (
                  <div
                    key={member.id}
                    className="flex items-center gap-2 px-2 py-1 hover:bg-accent/50 rounded transition-colors"
                  >
                    <div className="relative">
                      <Avatar className="h-5 w-5">
                        <AvatarImage src={member.avatar} />
                        <AvatarFallback className="text-xs">
                          {member.name.substring(0, 2).toUpperCase()}
                        </AvatarFallback>
                      </Avatar>
                      <div className={cn(
                        "absolute -bottom-0.5 -right-0.5 w-2 h-2 rounded-full border border-background",
                        member.isOnline ? "bg-green-500" : "bg-muted-foreground"
                      )} />
                    </div>
                    <div className="flex-1 min-w-0">
                      <span className="text-xs truncate block">
                        {member.name}
                      </span>
                      {!member.isOnline && member.lastSeen && (
                        <span className="text-xs text-muted-foreground truncate block">
                          {getRelativeTime(member.lastSeen.toISOString())}
                        </span>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </ScrollArea>
          </div>
        </div>
      </div>

      {/* Main Chat Area */}
      <div className="flex-1 flex flex-col">
        {/* Compact Channel Header */}
        <div className="h-12 border-b bg-card flex items-center justify-between px-3">
          <div className="flex items-center gap-2">
            <Hash className="h-4 w-4 text-muted-foreground" />
            <h2 className="font-medium text-sm">{activeChannel?.name}</h2>
            <Separator orientation="vertical" className="h-4" />
            <div className="flex items-center gap-1 text-xs text-muted-foreground">
              <Users className="h-3 w-3" />
              <span>{channelMembers.length}</span>
            </div>
            <ConnectionStatus isConnected={isConnected} />
          </div>

          <div className="flex items-center gap-1">
            <Button variant="ghost" size="icon" className="h-6 w-6">
              <Phone className="h-3 w-3" />
            </Button>
            <Button variant="ghost" size="icon" className="h-6 w-6">
              <Video className="h-3 w-3" />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              className="h-6 w-6"
              onClick={() => setShowChannelInfo(!showChannelInfo)}
            >
              <Info className="h-3 w-3" />
            </Button>
          </div>
        </div>

        {/* Messages Area - Takes all remaining space */}
        <div className="flex-1 overflow-hidden">
          <ScrollArea className="h-full px-3">
            <div className="py-3 space-y-2">
              {isLoadingMessages ? (
                <div className="space-y-3">
                  {[1, 2, 3, 4, 5].map(i => (
                    <div key={i} className="flex items-start gap-2">
                      <div className="w-6 h-6 bg-muted rounded-full animate-pulse" />
                      <div className="flex-1 space-y-1">
                        <div className="flex items-center gap-2">
                          <div className="h-3 bg-muted rounded w-16 animate-pulse" />
                          <div className="h-2 bg-muted rounded w-12 animate-pulse" />
                        </div>
                        <div className="h-3 bg-muted rounded w-3/4 animate-pulse" />
                      </div>
                    </div>
                  ))}
                </div>
              ) : messages.length > 0 ? (
                messages.map((message: ChatMessage, index: number) => (
                  <SlackMessage
                    key={message.id}
                    message={message}
                    channelId={channelId}
                    isCurrentUser={message.userId === currentUser?.id}
                    userColors={userColors}
                    onDelete={handleDeleteMessage}
                    onReply={handleReply}
                    onReaction={handleReaction}
                    replyTo={message.replyTo}
                    readBy={message.readBy}
                    previousMessage={index > 0 ? messages[index - 1] : null}
                  />
                ))
              ) : (
                <div className="flex items-center justify-center h-96">
                  <div className="text-center">
                    <MessageSquare className="h-8 w-8 mx-auto mb-2 text-muted-foreground" />
                    <h3 className="font-medium mb-1">No messages yet</h3>
                    <p className="text-sm text-muted-foreground">
                      Be the first to send a message in #{activeChannel?.name}
                    </p>
                  </div>
                </div>
              )}

              {/* Typing Indicator */}
              {typingUsers.size > 0 && (
                <TypingIndicator
                  userName={Array.from(typingUsers).join(', ')}
                />
              )}

              <div ref={messagesEndRef} />
            </div>
          </ScrollArea>
        </div>

        {/* Message Input - Fixed at Bottom */}
        <div className="border-t bg-card flex-shrink-0">
          {/* Reply Banner */}
          {replyingTo && (
            <div className="px-3 py-2 bg-muted/50 border-b">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2 text-sm">
                  <span className="text-muted-foreground">Replying to</span>
                  <span className="font-medium">{replyingTo.user}</span>
                  <span className="text-muted-foreground truncate max-w-xs">
                    {replyingTo.content}
                  </span>
                </div>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => setReplyingTo(null)}
                  className="h-5 w-5"
                >
                  <X className="h-3 w-3" />
                </Button>
              </div>
            </div>
          )}

          {/* Message Input */}
          <div className="p-3">
            {showFileUpload ? (
              <SlackFileUpload
                onFileSelect={handleFileSend}
                onCancel={() => setShowFileUpload(false)}
                isUploading={isUploading}
                onMessageSubmit={handleSendMessage}
                message={newMessage}
                onMessageChange={(e) => setNewMessage(e.target.value)}
                placeholder={`Message #${activeChannel?.name || 'channel'}`}
              />
            ) : (
              <form onSubmit={handleSendMessage} className="flex items-center gap-2">
                <Button
                  type="button"
                  variant="ghost"
                  size="icon"
                  className="h-8 w-8"
                  onClick={() => setShowFileUpload(true)}
                >
                  <Paperclip className="h-4 w-4" />
                </Button>

                <div className="flex-1">
                  <Input
                    ref={inputRef}
                    type="text"
                    placeholder={`Message #${activeChannel?.name || 'channel'}`}
                    value={newMessage}
                    onChange={(e) => setNewMessage(e.target.value)}
                    className="h-8"
                  />
                </div>

                <Button
                  type="button"
                  variant="ghost"
                  size="icon"
                  className="h-8 w-8"
                >
                  <Smile className="h-4 w-4" />
                </Button>

                <Button
                  type="submit"
                  disabled={!newMessage.trim()}
                  size="icon"
                  className="h-8 w-8"
                >
                  <Send className="h-4 w-4" />
                </Button>
              </form>
            )}
          </div>
        </div>
        </div>
      </div>
    </div>
  );
}

export default SlackChatInterface;
