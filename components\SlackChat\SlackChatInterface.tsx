"use client";

import React, { useState, useEffect, useRef } from 'react'
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import { createChannel, sendMessage, addReaction, getChannels, getMessages, generateInviteLink, getChannelMembers, deleteMessage } from '@/actions/chatActions'
import { Hash, Plus, Send, Paperclip, Smile, MoreVertical, Search, Info, Settings, Users, Bell, Phone, Video, Star, MessageSquare, Kanban, Layout, X } from 'lucide-react';
import { toast } from 'sonner';
import { useUser } from "@clerk/nextjs";
import { cn } from "@/lib/utils";
import { ConnectionStatus } from './ConnectionStatus';
import { SlackMessage } from './SlackMessage';
import { ChatSidebar } from './ChatSidebar';
import { pusherClient } from '@/lib/pusher';
import { TypingIndicator } from './TypingIndicator';
import { SlackFileUpload } from './SlackFileUpload';
import { ChatMessage } from '@/types/index';

// UI Components
import { Button } from "@/components/ui/button"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Input } from "@/components/ui/input"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Separator } from "@/components/ui/separator"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"

// Kanban Components
import KanbanBoard from '@/components/Managements/KanbanBoard'

// Types
interface Channel {
  id: string;
  name: string;
  unreadCount: number;
}

interface ChannelMember {
  id: string;
  name: string;
  avatar?: string;
  isOnline?: boolean;
  lastSeen?: Date;
}

interface ChatEvent {
  type: 'message' | 'messageDeleted' | 'typing';
  channelId: string;
  data: any;
}

function SlackChatInterface() {
  const params = useParams();
  const router = useRouter();
  const { user } = useUser();
  const channelId = params?.id as string;
  
  // State
  const [channels, setChannels] = useState<Channel[]>([]);
  const [activeChannel, setActiveChannel] = useState<Channel | null>(null);
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [newMessage, setNewMessage] = useState('');
  const [channelMembers, setChannelMembers] = useState<ChannelMember[]>([]);
  const [isConnected, setIsConnected] = useState(false);
  const [typingUsers, setTypingUsers] = useState<Set<string>>(new Set());
  const [showFileUpload, setShowFileUpload] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [replyingTo, setReplyingTo] = useState<ChatMessage | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [showChannelInfo, setShowChannelInfo] = useState(false);
  const [isLoadingMessages, setIsLoadingMessages] = useState(false);
  const [isLoadingChannels, setIsLoadingChannels] = useState(true);
  const [showKanban, setShowKanban] = useState(false);
  const [activeView, setActiveView] = useState<'chat' | 'kanban' | 'split'>('chat');
  
  // Refs
  const scrollRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Current user data
  const currentUser = user ? {
    id: user.id,
    fullName: user.fullName || user.username || 'Anonymous',
    username: user.username || 'anonymous',
    avatar: user.imageUrl || ''
  } : null;

  // User colors for avatars
  const userColors = {
    owner: "bg-blue-500 text-white",
    member: "bg-green-500 text-white",
    guest: "bg-gray-500 text-white"
  };

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // Focus input when component mounts
  useEffect(() => {
    inputRef.current?.focus();
  }, []);

  // Handle typing indicators
  useEffect(() => {
    let typingTimer: NodeJS.Timeout | undefined;

    const handleTyping = () => {
      if (channelId && currentUser && newMessage.trim()) {
        // Send typing indicator via API instead of direct pusher trigger
        fetch('/api/slackchat/typing', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            channelId,
            userId: currentUser.id,
            userName: currentUser.fullName
          })
        }).catch(console.error);
      }
    };

    if (newMessage.trim()) {
      handleTyping();
      if (typingTimer) {
        clearTimeout(typingTimer);
      }
      typingTimer = setTimeout(() => {
        // Stop typing indicator after 3 seconds
      }, 3000);
    }

    return () => {
      if (typingTimer) {
        clearTimeout(typingTimer);
      }
    };
  }, [newMessage, channelId, currentUser]);

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Escape to cancel reply
      if (e.key === 'Escape' && replyingTo) {
        setReplyingTo(null);
      }

      // Ctrl/Cmd + K to focus search
      if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
        e.preventDefault();
        // Focus search input if it exists
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [replyingTo]);

  // Fetch channels on mount
  useEffect(() => {
    fetchChannels();
  }, []);

  // Set active channel when channelId changes
  useEffect(() => {
    if (channelId && channels.length > 0) {
      const channel = channels.find(c => c.id === channelId);
      if (channel) {
        setActiveChannel(channel);
        fetchMessages(channelId);
        fetchChannelMembers(channelId);
      }
    }
  }, [channelId, channels]);

  // Pusher connection for real-time updates
  useEffect(() => {
    if (!channelId) return;

    const channel = pusherClient.subscribe(`chat-${channelId}`);
    setIsConnected(true);

    // Handle new messages
    channel.bind('message', (data: ChatEvent) => {
      setMessages(prev => {
        if (prev.some(m => m.id === data.data.id)) return prev;
        return [...prev, data.data];
      });
    });

    // Handle deleted messages
    channel.bind('messageDeleted', (data: ChatEvent) => {
      setMessages(prev => prev.filter(m => m.id !== data.data.messageId));
    });

    // Handle typing indicators
    channel.bind('typing', (data: any) => {
      if (data.userId !== currentUser?.id) {
        setTypingUsers(prev => new Set([...prev, data.userName]));
        setTimeout(() => {
          setTypingUsers(prev => {
            const newSet = new Set(prev);
            newSet.delete(data.userName);
            return newSet;
          });
        }, 3000);
      }
    });

    return () => {
      pusherClient.unsubscribe(`chat-${channelId}`);
      setIsConnected(false);
    };
  }, [channelId, currentUser?.id]);

  const fetchChannels = async () => {
    try {
      setIsLoadingChannels(true);
      const result = await getChannels();
      if (result.success) {
        setChannels(result.channels.map(channel => ({
          id: channel.id,
          name: channel.name,
          unreadCount: 0
        })));

        if (result.channels.length > 0 && !channelId) {
          router.push(`/hr/workspace/chats/${result.channels[0].id}`);
        }
      } else {
        toast.error("Failed to fetch channels");
      }
    } catch (error) {
      console.error('Error fetching channels:', error);
      toast.error("An error occurred while fetching channels");
    } finally {
      setIsLoadingChannels(false);
    }
  };

  const fetchMessages = async (id: string) => {
    try {
      setIsLoadingMessages(true);
      const result = await getMessages(id);
      if (result.success && result.messages) {
        setMessages(result.messages as ChatMessage[]);
      } else {
        console.error(result.error);
        toast.error("Failed to fetch messages");
      }
    } catch (error) {
      console.error('Error fetching messages:', error);
      toast.error("An error occurred while fetching messages");
    } finally {
      setIsLoadingMessages(false);
    }
  };

  const fetchChannelMembers = async (id: string) => {
    try {
      const result = await getChannelMembers(id);
      if (result.success) {
        setChannelMembers(result.members);
      } else {
        toast.error("Failed to fetch channel members");
      }
    } catch (error) {
      console.error('Error fetching channel members:', error);
      toast.error("An error occurred while fetching channel members");
    }
  };

  const handleChannelClick = (channel: Channel) => {
    router.push(`/hr/workspace/chats/${channel.id}`);
  };

  const handleSendMessage = async (e: React.FormEvent, fileUrl?: string) => {
    e.preventDefault();
    if (!activeChannel || !currentUser || (!newMessage.trim() && !fileUrl)) return;

    // Check for special commands
    const message = newMessage.trim();
    if (message.startsWith('/')) {
      handleSlashCommand(message);
      return;
    }

    const messageData = {
      channelId: activeChannel.id,
      content: message,
      userId: currentUser.id,
      fileKey: fileUrl ? fileUrl.split('/').pop() : undefined,
      fileType: fileUrl ? getFileType(fileUrl) : undefined,
      parentId: replyingTo?.id
    };

    try {
      const result = await sendMessage(messageData);
      if (result.success) {
        setNewMessage('');
        setShowFileUpload(false);
        setReplyingTo(null);
        inputRef.current?.focus();
        toast.success("Message sent!");
      } else {
        toast.error(result.error || "Failed to send message");
      }
    } catch (error) {
      toast.error("An error occurred while sending the message");
    }
  };

  const handleSlashCommand = async (command: string) => {
    const cmd = command.toLowerCase();

    if (cmd.startsWith('/kanban') || cmd.startsWith('/board') || cmd.startsWith('/project')) {
      // Send the kanban command as a message
      const messageData = {
        channelId: activeChannel!.id,
        content: command,
        userId: currentUser!.id,
        parentId: replyingTo?.id
      };

      try {
        const result = await sendMessage(messageData);
        if (result.success) {
          setNewMessage('');
          setReplyingTo(null);
          inputRef.current?.focus();
          toast.success("Kanban board shared!");
        }
      } catch (error) {
        toast.error("Failed to share kanban board");
      }
    } else if (cmd === '/help') {
      toast.info("Available commands: /kanban, /board, /project, /kanban-compact");
    } else {
      toast.error("Unknown command. Type /help for available commands.");
    }
  };

  const handleFileSend = async (fileUrl: string, fileType?: string) => {
    if (!activeChannel || !currentUser) return;

    const messageData = {
      channelId: activeChannel.id,
      content: newMessage.trim(),
      userId: currentUser.id,
      fileKey: fileUrl.split('/').pop(),
      fileType: fileType || getFileType(fileUrl),
      parentId: replyingTo?.id
    };

    try {
      const result = await sendMessage(messageData);
      if (result.success) {
        setNewMessage('');
        setShowFileUpload(false);
        setReplyingTo(null);
        inputRef.current?.focus();
        toast.success("File sent successfully!");
      } else {
        toast.error(result.error || "Failed to send file");
      }
    } catch (error) {
      toast.error("An error occurred while sending the file");
    }
  };

  const handleDeleteMessage = async (messageId: string) => {
    if (!activeChannel) return;

    try {
      const result = await deleteMessage(messageId, activeChannel.id);
      if (result.success) {
        setMessages(prev => prev.filter(m => m.id !== messageId));
        toast.success("Message deleted");
      } else {
        toast.error("Failed to delete message");
      }
    } catch (error) {
      toast.error("An error occurred while deleting the message");
    }
  };

  const handleReply = (messageId: string) => {
    const message = messages.find(m => m.id === messageId);
    if (message) {
      setReplyingTo(message);
      inputRef.current?.focus();
    }
  };

  const handleReaction = async (messageId: string, emoji: string) => {
    try {
      const result = await addReaction(messageId, emoji);
      if (result.success) {
        // Fetch updated messages to get the latest reactions
        if (activeChannel) {
          fetchMessages(activeChannel.id);
        }
        toast.success("Reaction added");
      }
    } catch (error) {
      toast.error("Failed to add reaction");
    }
  };

  const getFileType = (url: string) => {
    const extension = url.split('.').pop()?.toLowerCase();
    if (['jpg', 'jpeg', 'png', 'gif', 'webp'].includes(extension || '')) return 'image';
    if (extension === 'pdf') return 'pdf';
    if (['csv', 'xlsx', 'xls'].includes(extension || '')) return 'spreadsheet';
    return 'other';
  };

  const formatTime = (timestamp: string) => {
    const date = new Date(timestamp);
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  const getRelativeTime = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);
    
    if (diffInSeconds < 60) return 'now';
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m`;
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h`;
    return `${Math.floor(diffInSeconds / 86400)}d`;
  };

  return (
    <TooltipProvider>
      <div className="h-screen flex bg-background">
        {/* Compact Sidebar */}
        <ChatSidebar
          channels={channels}
          activeChannel={activeChannel}
          channelMembers={channelMembers}
          currentUser={currentUser}
          isLoadingChannels={isLoadingChannels}
          onChannelClick={handleChannelClick}
          onCreateChannel={() => toast.info("Create channel feature coming soon!")}
        />

        {/* Main Chat Area */}
        <div className="flex-1 flex flex-col">
          {/* Enhanced Channel Header */}
          <Card className="rounded-none border-x-0 border-t-0">
            <CardHeader className="py-3 px-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="flex items-center gap-2">
                    <Hash className="h-5 w-5 text-muted-foreground" />
                    <CardTitle className="text-lg font-semibold">{activeChannel?.name}</CardTitle>
                  </div>
                  <Separator orientation="vertical" className="h-6" />
                  <div className="flex items-center gap-1 text-sm text-muted-foreground">
                    <Users className="h-4 w-4" />
                    <span>{channelMembers.length} members</span>
                  </div>
                  <Badge variant="secondary" className="text-xs">
                    <ConnectionStatus isConnected={isConnected} />
                  </Badge>
                </div>

                <div className="flex items-center gap-1">
                  {/* View Toggle */}
                  <Tabs value={activeView} onValueChange={(value) => setActiveView(value as 'chat' | 'kanban' | 'split')}>
                    <TabsList className="grid w-full grid-cols-3 h-8">
                      <TabsTrigger value="chat" className="text-xs">
                        <MessageSquare className="h-3 w-3 mr-1" />
                        Chat
                      </TabsTrigger>
                      <TabsTrigger value="kanban" className="text-xs">
                        <Kanban className="h-3 w-3 mr-1" />
                        Board
                      </TabsTrigger>
                      <TabsTrigger value="split" className="text-xs">
                        <Layout className="h-3 w-3 mr-1" />
                        Split
                      </TabsTrigger>
                    </TabsList>
                  </Tabs>

                  <Separator orientation="vertical" className="h-6 mx-2" />

                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button variant="ghost" size="icon" className="h-8 w-8">
                        <Phone className="h-4 w-4" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>Start a call</TooltipContent>
                  </Tooltip>

                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button variant="ghost" size="icon" className="h-8 w-8">
                        <Video className="h-4 w-4" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>Start a video call</TooltipContent>
                  </Tooltip>

                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-8 w-8"
                        onClick={() => setShowChannelInfo(!showChannelInfo)}
                      >
                        <Info className="h-4 w-4" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>Channel details</TooltipContent>
                  </Tooltip>
                </div>
              </div>
            </CardHeader>
          </Card>

          {/* Main Content Area with View Switching */}
          <div className="flex-1 flex">
            {activeView === 'chat' && (
              <div className="flex-1 flex flex-col">
                <ScrollArea className="flex-1 px-4">
                  <div className="py-4 space-y-1">
                    {isLoadingMessages ? (
                      <div className="space-y-4">
                        {[1, 2, 3, 4, 5].map(i => (
                          <div key={i} className="flex items-start gap-3">
                            <div className="w-9 h-9 bg-slate-200 rounded-full animate-pulse" />
                            <div className="flex-1 space-y-2">
                              <div className="flex items-center gap-2">
                                <div className="h-4 bg-slate-200 rounded w-20 animate-pulse" />
                                <div className="h-3 bg-slate-200 rounded w-16 animate-pulse" />
                              </div>
                              <div className="h-4 bg-slate-200 rounded w-3/4 animate-pulse" />
                            </div>
                          </div>
                        ))}
                      </div>
                    ) : messages.length > 0 ? (
                      messages.map((message: ChatMessage, index: number) => (
                        <SlackMessage
                          key={message.id}
                          message={message}
                          channelId={channelId}
                          isCurrentUser={message.userId === currentUser?.id}
                          userColors={userColors}
                          onDelete={handleDeleteMessage}
                          onReply={handleReply}
                          onReaction={handleReaction}
                          replyTo={message.replyTo}
                          readBy={message.readBy}
                          previousMessage={index > 0 ? messages[index - 1] : null}
                        />
                      ))
                    ) : (
                      <div className="flex-1 flex items-center justify-center min-h-[400px]">
                        <Card className="p-8 text-center">
                          <MessageSquare className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                          <CardTitle className="mb-2">No messages yet</CardTitle>
                          <p className="text-sm text-muted-foreground">
                            Be the first to send a message in #{activeChannel?.name}
                          </p>
                        </Card>
                      </div>
                    )}

                    {/* Typing Indicator */}
                    {typingUsers.size > 0 && (
                      <TypingIndicator
                        userName={Array.from(typingUsers).join(', ')}
                      />
                    )}

                    <div ref={messagesEndRef} />
                  </div>
                </ScrollArea>
              </div>
            )}

            {activeView === 'kanban' && (
              <div className="flex-1 p-4">
                <Card className="h-full">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Kanban className="h-5 w-5" />
                      Project Board - {activeChannel?.name}
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="h-full p-0">
                    <div className="h-full overflow-hidden">
                      <KanbanBoard />
                    </div>
                  </CardContent>
                </Card>
              </div>
            )}

            {activeView === 'split' && (
              <div className="flex-1 flex gap-4 p-4">
                {/* Chat Section */}
                <div className="flex-1 flex flex-col">
                  <Card className="flex-1">
                    <CardHeader className="py-3">
                      <CardTitle className="text-sm flex items-center gap-2">
                        <MessageSquare className="h-4 w-4" />
                        Chat
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="flex-1 p-0">
                      <ScrollArea className="h-[400px] px-4">
                        <div className="py-2 space-y-1">
                          {messages.slice(-10).map((message: ChatMessage, index: number) => (
                            <SlackMessage
                              key={message.id}
                              message={message}
                              channelId={channelId}
                              isCurrentUser={message.userId === currentUser?.id}
                              userColors={userColors}
                              onDelete={handleDeleteMessage}
                              onReply={handleReply}
                              onReaction={handleReaction}
                              replyTo={message.replyTo}
                              readBy={message.readBy}
                              previousMessage={index > 0 ? messages[index - 1] : null}
                            />
                          ))}
                          <div ref={messagesEndRef} />
                        </div>
                      </ScrollArea>
                    </CardContent>
                  </Card>
                </div>

                {/* Kanban Section */}
                <div className="flex-1">
                  <Card className="h-full">
                    <CardHeader className="py-3">
                      <CardTitle className="text-sm flex items-center gap-2">
                        <Kanban className="h-4 w-4" />
                        Project Board
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="h-full p-0">
                      <div className="h-[400px] overflow-hidden">
                        <KanbanBoard />
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </div>
            )}

          {/* Message Input - Only show for chat and split views */}
          {(activeView === 'chat' || activeView === 'split') && (
            <div className="border-t bg-background">
              {/* Reply Banner */}
              {replyingTo && (
                <Card className="rounded-none border-x-0 border-b-0">
                  <CardContent className="py-2 px-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2 text-sm">
                        <span className="text-muted-foreground">Replying to</span>
                        <Badge variant="secondary">{replyingTo.user}</Badge>
                        <span className="text-muted-foreground truncate max-w-xs">
                          {replyingTo.content}
                        </span>
                      </div>
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => setReplyingTo(null)}
                        className="h-6 w-6"
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Enhanced Message Input */}
              <div className="p-4">
                {showFileUpload ? (
                  <SlackFileUpload
                    onFileSelect={handleFileSend}
                    onCancel={() => setShowFileUpload(false)}
                    isUploading={isUploading}
                    onMessageSubmit={handleSendMessage}
                    message={newMessage}
                    onMessageChange={(e) => setNewMessage(e.target.value)}
                    placeholder={`Message #${activeChannel?.name || 'channel'}`}
                  />
                ) : (
                  <Card>
                    <CardContent className="p-3">
                      <form onSubmit={handleSendMessage} className="flex items-center gap-3">
                        <Button
                          type="button"
                          variant="ghost"
                          size="icon"
                          className="h-9 w-9"
                          onClick={() => setShowFileUpload(true)}
                        >
                          <Paperclip className="h-4 w-4" />
                        </Button>

                        <div className="flex-1 relative">
                          <Input
                            ref={inputRef}
                            type="text"
                            placeholder={`Message #${activeChannel?.name || 'channel'} (try /kanban, /board, /help)`}
                            value={newMessage}
                            onChange={(e) => setNewMessage(e.target.value)}
                            className="border-0 focus-visible:ring-0 focus-visible:ring-offset-0 bg-muted/50"
                          />
                          {newMessage.startsWith('/') && (
                            <div className="absolute top-full left-0 right-0 mt-1 p-2 bg-popover border rounded-md shadow-md text-xs z-10">
                              <div className="space-y-1">
                                <div><strong>/kanban</strong> - Share project kanban board</div>
                                <div><strong>/board</strong> - Share project board</div>
                                <div><strong>/kanban-compact</strong> - Share compact board view</div>
                                <div><strong>/help</strong> - Show available commands</div>
                              </div>
                            </div>
                          )}
                        </div>

                        <Button
                          type="button"
                          variant="ghost"
                          size="icon"
                          className="h-9 w-9"
                        >
                          <Smile className="h-4 w-4" />
                        </Button>

                        <Button
                          type="submit"
                          disabled={!newMessage.trim()}
                          size="icon"
                          className="h-9 w-9"
                        >
                          <Send className="h-4 w-4" />
                        </Button>
                      </form>
                    </CardContent>
                  </Card>
                )}
              </div>
            </div>
          )}

          {/* Enhanced Channel Info Sidebar */}
          {showChannelInfo && (
            <div className="w-80 border-l">
              <Card className="h-full rounded-none border-x-0 border-t-0">
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-lg">Channel Details</CardTitle>
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => setShowChannelInfo(false)}
                      className="h-8 w-8"
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div>
                    <h3 className="font-semibold mb-3">About this channel</h3>
                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                      <Hash className="h-4 w-4" />
                      <span>{activeChannel?.name}</span>
                    </div>
                    <p className="text-sm text-muted-foreground mt-2">
                      This is a channel for team collaboration and project discussions.
                    </p>
                  </div>

                  <Separator />

                  <div>
                    <h4 className="font-medium mb-3">Members ({channelMembers.length})</h4>
                    <ScrollArea className="h-48">
                      <div className="space-y-2">
                        {channelMembers.map(member => (
                          <div key={member.id} className="flex items-center gap-3 p-2 rounded-lg hover:bg-muted/50 transition-colors">
                            <div className="relative">
                              <Avatar className="h-8 w-8">
                                <AvatarImage src={member.avatar} />
                                <AvatarFallback className="text-xs">
                                  {member.name.substring(0, 2).toUpperCase()}
                                </AvatarFallback>
                              </Avatar>
                              {member.isOnline && (
                                <div className="absolute -bottom-0.5 -right-0.5 w-3 h-3 bg-green-400 rounded-full border-2 border-background" />
                              )}
                            </div>
                            <div className="flex-1 min-w-0">
                              <p className="text-sm font-medium truncate">{member.name}</p>
                              <p className="text-xs text-muted-foreground">
                                {member.isOnline ? 'Active now' : 'Offline'}
                              </p>
                            </div>
                          </div>
                        ))}
                      </div>
                    </ScrollArea>
                  </div>

                  <Separator />

                  <div>
                    <h4 className="font-medium mb-3">Quick Actions</h4>
                    <div className="space-y-2">
                      <Button variant="outline" size="sm" className="w-full justify-start">
                        <Star className="h-4 w-4 mr-2" />
                        Star Channel
                      </Button>
                      <Button variant="outline" size="sm" className="w-full justify-start">
                        <Bell className="h-4 w-4 mr-2" />
                        Notification Settings
                      </Button>
                      <Button variant="outline" size="sm" className="w-full justify-start">
                        <Users className="h-4 w-4 mr-2" />
                        Add Members
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}
          </div>
        </div>
      </div>
    </TooltipProvider>
  );
}

export default SlackChatInterface;
