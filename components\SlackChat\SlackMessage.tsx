"use client";

import { useState, useEffect, useCallback } from "react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { But<PERSON> } from "@/components/ui/button";
import { MessageSquare, Trash2, MoreHorizontal, Reply, Download, ExternalLink, FileText, Image as ImageIcon, FileSpreadsheet, Play, Pause } from "lucide-react";
import { cn } from "@/lib/utils";
import { ChatMessage } from "@/types/index";
import { Dialog, DialogContent } from "@/components/ui/dialog";
import { Tooltip, TooltipContent, TooltipTrigger } from "@/components/ui/tooltip";
import Image from "next/image";

interface SlackMessageProps {
  message: ChatMessage;
  channelId: string;
  isCurrentUser: boolean;
  userColors: Record<string, string>;
  onDelete: (messageId: string) => void;
  onReply: (messageId: string) => void;
  onReaction: (messageId: string, emoji: string) => void;
  replyTo?: {
    id: string;
    content: string;
    user: string;
  };
  readBy?: string[];
  showAvatar?: boolean;
  isGrouped?: boolean;
  previousMessage?: ChatMessage | null;
}

const UPLOADTHING_URL = "https://uploadthing.com/f/";
const emojis = ['👍', '❤️', '😂', '😮', '😢', '😡'];

export function SlackMessage({
  message,
  channelId,
  isCurrentUser,
  userColors,
  onDelete,
  onReply,
  onReaction,
  replyTo,
  readBy = [],
  showAvatar = true,
  isGrouped = false,
  previousMessage = null,
}: SlackMessageProps) {
  const [showImagePreview, setShowImagePreview] = useState(false);
  const [showActions, setShowActions] = useState(false);
  const [showReactions, setShowReactions] = useState(false);
  const [blobUrl, setBlobUrl] = useState<string | null>(null);
  const [previewError, setPreviewError] = useState(false);
  const [isHovered, setIsHovered] = useState(false);

  const userName = message.user || 'Anonymous';
  const userInitials = userName.substring(0, 2).toUpperCase();

  // Check if this message should be grouped with the previous one
  const shouldGroup = previousMessage &&
    previousMessage.userId === message.userId &&
    !replyTo &&
    !previousMessage.replyTo &&
    (new Date(message.timestamp).getTime() - new Date(previousMessage.timestamp).getTime()) < 300000; // 5 minutes

  const fetchAndCreateBlob = useCallback(async (url: string) => {
    try {
      const response = await fetch(url);
      const blob = await response.blob();
      const blobUrl = URL.createObjectURL(blob);
      setBlobUrl(blobUrl);
      return blobUrl;
    } catch (error) {
      console.error('Error creating blob URL:', error);
      setPreviewError(true);
      return null;
    }
  }, []);

  // Cleanup blob URL on unmount
  useEffect(() => {
    return () => {
      if (blobUrl) {
        URL.revokeObjectURL(blobUrl);
      }
    };
  }, [blobUrl]);

  const formatMessageTime = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const isToday = date.toDateString() === now.toDateString();
    
    if (isToday) {
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } else {
      return date.toLocaleDateString([], { 
        month: 'short', 
        day: 'numeric',
        hour: '2-digit', 
        minute: '2-digit' 
      });
    }
  };

  const getFileIcon = (fileType: string, fileName: string) => {
    if (fileType?.startsWith('image/')) {
      return <ImageIcon className="h-5 w-5 text-blue-500" />;
    } else if (fileType === 'application/pdf') {
      return <FileText className="h-5 w-5 text-red-500" />;
    } else if (fileType?.includes('spreadsheet') || fileName?.endsWith('.csv') || fileName?.endsWith('.xlsx')) {
      return <FileSpreadsheet className="h-5 w-5 text-green-500" />;
    }
    return <FileText className="h-5 w-5 text-slate-500" />;
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const renderFilePreview = () => {
    const fileUrl = message.fileUrl || (message.fileKey ? `${UPLOADTHING_URL}${message.fileKey}` : null);
    if (!fileUrl) return null;

    const fileName = message.fileName || fileUrl.split('/').pop() || 'File';
    const fileType = message.fileType || '';

    // Image preview
    if (fileType.startsWith('image/')) {
      return (
        <div className="mt-2 max-w-sm">
          <div 
            className="relative rounded-lg overflow-hidden border cursor-pointer group"
            onClick={() => setShowImagePreview(true)}
          >
            <Image
              src={fileUrl}
              alt={fileName}
              width={400}
              height={300}
              className="object-cover hover:opacity-95 transition-opacity"
              unoptimized
            />
            <div className="absolute inset-0 bg-black/0 group-hover:bg-black/10 transition-colors flex items-center justify-center">
              <ExternalLink className="h-6 w-6 text-white opacity-0 group-hover:opacity-100 transition-opacity" />
            </div>
          </div>
          
          <Dialog open={showImagePreview} onOpenChange={setShowImagePreview}>
            <DialogContent className="max-w-4xl h-[80vh] p-0">
              <div className="relative w-full h-full bg-black">
                <Image
                  src={fileUrl}
                  alt={fileName}
                  fill
                  className="object-contain"
                  unoptimized
                  quality={100}
                  priority
                />
              </div>
            </DialogContent>
          </Dialog>
        </div>
      );
    }

    // File attachment
    return (
      <div className="mt-2 max-w-sm">
        <div className="flex items-center gap-3 p-3 border rounded-lg bg-slate-50 hover:bg-slate-100 transition-colors">
          {getFileIcon(fileType, fileName)}
          <div className="flex-1 min-w-0">
            <p className="text-sm font-medium text-slate-900 truncate">{fileName}</p>
            <p className="text-xs text-slate-500">
              {fileType.split('/')[1]?.toUpperCase() || 'FILE'}
            </p>
          </div>
          <div className="flex gap-1">
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-8 w-8"
                  onClick={() => window.open(fileUrl, '_blank')}
                >
                  <ExternalLink className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>Open file</TooltipContent>
            </Tooltip>
            
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-8 w-8"
                  onClick={() => {
                    const link = document.createElement('a');
                    link.href = fileUrl;
                    link.download = fileName;
                    link.click();
                  }}
                >
                  <Download className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>Download</TooltipContent>
            </Tooltip>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div
      className={cn(
        "group relative px-4 py-2 hover:bg-slate-50 transition-all duration-200 ease-in-out",
        replyTo && "ml-8 border-l-2 border-slate-200 pl-4",
        shouldGroup ? "py-1" : "py-2"
      )}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* Reply indicator */}
      {replyTo && (
        <div className="flex items-center gap-2 mb-1 text-xs text-slate-500">
          <Reply className="h-3 w-3" />
          <span>Replying to <span className="font-medium text-blue-600">{replyTo.user}</span></span>
          <span className="truncate max-w-xs">{replyTo.content}</span>
        </div>
      )}

      <div className="flex items-start gap-3">
        {/* Avatar */}
        {shouldGroup ? (
          <div className="w-9 h-9 flex items-center justify-center">
            <span className="text-xs text-slate-400 hover:text-slate-600 cursor-pointer">
              {formatMessageTime(message.timestamp)}
            </span>
          </div>
        ) : (
          <Avatar className="h-9 w-9 mt-0.5">
            <AvatarImage src={message.avatar} alt={userName} />
            <AvatarFallback className={cn(
              "text-sm font-medium",
              isCurrentUser ? userColors.owner : userColors.member
            )}>
              {userInitials}
            </AvatarFallback>
          </Avatar>
        )}

        {/* Message Content */}
        <div className="flex-1 min-w-0">
          {/* Message Header - Only show for non-grouped messages */}
          {!shouldGroup && (
            <div className="flex items-baseline gap-2 mb-1">
              <span className="font-semibold text-slate-900 text-sm">{userName}</span>
              <span className="text-xs text-slate-500">
                {formatMessageTime(message.timestamp)}
              </span>
            </div>
          )}

          {/* Message Text */}
          {message.content && (
            <div className="text-sm text-slate-900 leading-relaxed mb-1">
              {message.content}
            </div>
          )}

          {/* File Attachment */}
          {(message.fileKey || message.fileUrl) && renderFilePreview()}

          {/* Reactions */}
          {message.reactions && message.reactions.length > 0 && (
            <div className="flex items-center gap-1 mt-2">
              {message.reactions.map((reaction: any, index: number) => (
                <button
                  key={index}
                  onClick={() => onReaction(message.id, reaction.emoji)}
                  className="flex items-center gap-1 px-2 py-1 rounded-full text-xs bg-blue-50 hover:bg-blue-100 border border-blue-200 transition-colors"
                >
                  <span>{reaction.emoji}</span>
                  <span className="text-blue-700 font-medium">{reaction.count}</span>
                </button>
              ))}
            </div>
          )}
        </div>

        {/* Message Actions */}
        {isHovered && (
          <div className="absolute top-0 right-4 bg-white border rounded-lg shadow-lg flex items-center animate-in fade-in-0 slide-in-from-right-2 duration-200">
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-8 w-8"
                  onClick={() => setShowReactions(!showReactions)}
                >
                  <span className="text-sm">😊</span>
                </Button>
              </TooltipTrigger>
              <TooltipContent>Add reaction</TooltipContent>
            </Tooltip>

            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-8 w-8"
                  onClick={() => onReply(message.id)}
                >
                  <Reply className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>Reply</TooltipContent>
            </Tooltip>

            {isCurrentUser && (
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-8 w-8 text-red-500 hover:text-red-700"
                    onClick={() => onDelete(message.id)}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>Delete message</TooltipContent>
              </Tooltip>
            )}

            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-8 w-8"
                  onClick={() => setShowActions(!showActions)}
                >
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>More actions</TooltipContent>
            </Tooltip>
          </div>
        )}

        {/* Emoji Picker */}
        {showReactions && (
          <div className="absolute top-8 right-4 bg-white border rounded-lg shadow-lg p-2 flex gap-1 z-10">
            {emojis.map(emoji => (
              <button
                key={emoji}
                onClick={() => {
                  onReaction(message.id, emoji);
                  setShowReactions(false);
                }}
                className="p-1 hover:bg-slate-100 rounded transition-colors"
              >
                {emoji}
              </button>
            ))}
          </div>
        )}
      </div>

      {/* Read receipts for current user messages */}
      {isCurrentUser && readBy && readBy.length > 0 && (
        <div className="flex justify-end mt-1">
          <div className="flex items-center gap-1">
            {readBy.slice(0, 3).map((reader, i) => (
              <div key={i} className="w-4 h-4 rounded-full bg-blue-500 text-white text-xs flex items-center justify-center">
                ✓
              </div>
            ))}
            {readBy.length > 3 && (
              <span className="text-xs text-slate-500">+{readBy.length - 3}</span>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
